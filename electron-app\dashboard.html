<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WC Arena Core Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e, #16213e);
            color: #ffffff;
            height: 100vh;
            overflow: hidden;
        }

        .dashboard-container {
            display: grid;
            grid-template-columns: 300px 1fr;
            grid-template-rows: 80px 1fr;
            height: 100vh;
            gap: 1px;
            background: #0f0f23;
        }

        .header {
            grid-column: 1 / -1;
            background: linear-gradient(90deg, #2d1b69, #11998e);
            display: flex;
            align-items: center;
            padding: 0 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .logo img {
            width: 40px;
            height: 40px;
        }

        .logo h1 {
            font-size: 24px;
            font-weight: bold;
            background: linear-gradient(45deg, #ffd700, #ffed4e);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .sidebar {
            background: #1a1a2e;
            padding: 20px;
            border-right: 1px solid #2d2d44;
        }

        .main-content {
            background: #16213e;
            padding: 20px;
            overflow-y: auto;
        }

        .user-section {
            margin-bottom: 30px;
            padding: 15px;
            background: rgba(255,255,255,0.05);
            border-radius: 10px;
            border: 1px solid rgba(255,255,255,0.1);
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .user-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            border: 2px solid #ffd700;
        }

        .username {
            font-size: 18px;
            font-weight: bold;
            color: #ffd700;
        }

        .user-level {
            font-size: 14px;
            color: #aaa;
        }

        .game-status-section {
            margin-bottom: 30px;
        }

        .section-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #ffd700;
            border-bottom: 2px solid #ffd700;
            padding-bottom: 5px;
        }

        .status-playing .status-indicator {
            background: #4CAF50;
            animation: pulse 2s infinite;
        }

        .status-idle .status-indicator {
            background: #666;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 10px;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .game-stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .game-stat-card {
            background: rgba(255,255,255,0.05);
            border-radius: 10px;
            padding: 20px;
            border: 1px solid rgba(255,255,255,0.1);
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .game-stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.3);
        }

        .game-title {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #ffd700;
            text-align: center;
        }

        .stat-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            padding: 5px 0;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .stat-label {
            color: #ccc;
        }

        .stat-value {
            font-weight: bold;
            color: #fff;
        }

        .stat-value.won {
            color: #4CAF50;
        }

        .stat-value.lost {
            color: #f44336;
        }

        .detection-status {
            background: rgba(255,255,255,0.05);
            border-radius: 10px;
            padding: 20px;
            border: 1px solid rgba(255,255,255,0.1);
        }

        .detection-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            padding: 10px;
            background: rgba(255,255,255,0.02);
            border-radius: 5px;
        }

        .detection-status-indicator {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: #4CAF50;
        }

        .detection-status-indicator.inactive {
            background: #666;
        }

        .btn {
            background: linear-gradient(45deg, #2d1b69, #11998e);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s;
        }

        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 3px 10px rgba(0,0,0,0.3);
        }

        .btn-primary {
            background: linear-gradient(45deg, #ffd700, #ffed4e);
            color: #000;
        }

        .login-prompt {
            text-align: center;
            padding: 20px;
        }

        .recent-activity {
            background: rgba(255,255,255,0.05);
            border-radius: 10px;
            padding: 20px;
            border: 1px solid rgba(255,255,255,0.1);
            max-height: 300px;
            overflow-y: auto;
        }

        .activity-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            margin-bottom: 5px;
            background: rgba(255,255,255,0.02);
            border-radius: 5px;
        }

        .activity-time {
            font-size: 12px;
            color: #aaa;
        }

        .scrollbar::-webkit-scrollbar {
            width: 8px;
        }

        .scrollbar::-webkit-scrollbar-track {
            background: rgba(255,255,255,0.1);
            border-radius: 4px;
        }

        .scrollbar::-webkit-scrollbar-thumb {
            background: rgba(255,255,255,0.3);
            border-radius: 4px;
        }

        .scrollbar::-webkit-scrollbar-thumb:hover {
            background: rgba(255,255,255,0.5);
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <header class="header">
            <div class="logo">
                <img src="../frontend/assets/img/ranks/emblem.png" alt="WC Arena">
                <h1>WC Arena Core</h1>
            </div>
        </header>

        <aside class="sidebar">
            <div class="user-section">
                <h3 class="section-title">User</h3>
                <div id="current-user">
                    <div class="login-prompt">
                        <button onclick="window.arenaDashboard.showLogin()" class="btn btn-primary">
                            Login to Arena
                        </button>
                    </div>
                </div>
            </div>

            <div class="game-status-section">
                <h3 class="section-title">Game Status</h3>
                <div id="game-status">
                    <div class="status-idle">
                        <div class="status-indicator idle"></div>
                        <div class="status-text">
                            <div class="game-name">Ready to play</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="detection-section">
                <h3 class="section-title">Detection Status</h3>
                <div class="detection-status">
                    <div class="detection-item">
                        <span>Game Process Monitor</span>
                        <div class="detection-status-indicator" id="process-monitor-status"></div>
                    </div>
                    <div class="detection-item">
                        <span>Log File Monitor</span>
                        <div class="detection-status-indicator" id="log-monitor-status"></div>
                    </div>
                    <div class="detection-item">
                        <span>Replay Analyzer</span>
                        <div class="detection-status-indicator" id="replay-analyzer-status"></div>
                    </div>
                </div>
            </div>
        </aside>

        <main class="main-content scrollbar">
            <section class="stats-section">
                <h2 class="section-title">Game Statistics</h2>
                <div class="game-stats-grid" id="game-stats">
                    <!-- Game stats will be populated here -->
                </div>
            </section>

            <section class="activity-section">
                <h2 class="section-title">Recent Activity</h2>
                <div class="recent-activity scrollbar" id="recent-activity">
                    <div class="activity-item">
                        <span>Arena Core Dashboard started</span>
                        <span class="activity-time">Just now</span>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <script>
        // Initialize the dashboard when the page loads
        document.addEventListener('DOMContentLoaded', () => {
            if (window.arenaDashboard) {
                window.arenaDashboard.init();
            }
        });
    </script>
</body>
</html>
