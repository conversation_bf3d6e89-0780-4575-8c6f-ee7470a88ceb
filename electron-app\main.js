const { app, BrowserWindow, shell, protocol, dialog, ipcMain } = require('electron');
const path = require('path');
const axios = require('axios');
const Store = require('electron-store');
const { SimplifiedAuthManager } = require('./src/simplified-auth-manager');
const { WindowManager } = require('./src/window-manager');
const { GameDetector } = require('./src/game-detector');
const { EnhancedTrayManager } = require('./src/enhanced-tray-manager');
const { GarrisonIntegration } = require('./src/garrison-integration');
const ScreenshotManager = require('./src/screenshot-manager');
const MatchResultManager = require('./src/match-result-manager');
const LightweightGameDetector = require('./src/lightweight-game-detector');

// Enable hardware acceleration and performance optimizations
app.commandLine.appendSwitch('--enable-hardware-acceleration');
app.commandLine.appendSwitch('--enable-gpu-rasterization');
app.commandLine.appendSwitch('--enable-zero-copy');
app.commandLine.appendSwitch('--ignore-gpu-blacklist');
app.commandLine.appendSwitch('--enable-features', 'VaapiVideoDecoder');
app.commandLine.appendSwitch('--disable-background-timer-throttling');
app.commandLine.appendSwitch('--disable-backgrounding-occluded-windows');
app.commandLine.appendSwitch('--disable-renderer-backgrounding');
app.commandLine.appendSwitch('--disable-features', 'TranslateUI');
app.commandLine.appendSwitch('--disable-ipc-flooding-protection');

// Performance optimizations for Electron
app.commandLine.appendSwitch('--max_old_space_size', '4096'); // Increase memory limit
app.commandLine.appendSwitch('--js-flags', '--max-old-space-size=4096');

// Register custom protocol scheme before app is ready
protocol.registerSchemesAsPrivileged([
  { scheme: 'warcraftarena', privileges: { secure: true, standard: true } }
]);

class WarcraftArenaApp {
  constructor() {
    // Set the correct app name for userData path
    app.setName('WC Arena Core');
    
    this.store = new Store({
      name: 'wc-arena-core-config',
      encryptionKey: 'wc-arena-core-secret-key', // In production, generate this securely
      defaults: {
        user: null,
        serverUrl: 'http://127.0.0.1:3001',  // Use port 3001 for API calls
        rememberLogin: true,
        minimizeToTray: true,
        closeToTray: true,
        autoFindGames: true,
        windowBounds: {
          width: 1400,
          height: 900
        }
      }
    });

    // Update server URL to use port 3001 for API calls and OAuth
    this.store.set('serverUrl', 'http://127.0.0.1:3001');
    
    // Set app start time for uptime tracking
    if (!this.store.get('appStartTime')) {
      this.store.set('appStartTime', Date.now());
    }
    
    this.authManager = new SimplifiedAuthManager(this.store);
    this.gameDetector = new GameDetector();
    this.lightweightGameDetector = new LightweightGameDetector();
    this.windowManager = new WindowManager(this.store);
    
    // Initialize screenshot manager with app data path
    const appDataPath = app.getPath('userData');
    this.screenshotManager = new ScreenshotManager(this.gameDetector, appDataPath, this.authManager);
    
    // Initialize match result manager
    const serverUrl = this.store.get('serverUrl', 'http://127.0.0.1:3001');
    this.matchResultManager = new MatchResultManager(
      this.gameDetector,
      this.screenshotManager,
      this.authManager,
      serverUrl
    );
    
    this.trayManager = new EnhancedTrayManager(app, this.windowManager, this.gameDetector, this.store, this.screenshotManager);
    
    // Set game detector reference in tray manager
    this.trayManager.gameDetector = this.gameDetector;
    
    // Initialize garrison integration for enhanced taskbar features
    this.garrisonIntegration = new GarrisonIntegration(this.trayManager, this.store);
    
    // Handle single instance logic
    this.handleSingleInstance();
    
    this.initializeApp();
  }

  initializeApp() {
    // Set up critical managers first
    this.setupSingleInstanceHandling();
    this.registerProtocol();
    
    // Set up IPC handlers early
    this.setupIPC();
    
    // Set up window event handlers after window is created
    app.whenReady().then(async () => {
      console.log('🚀 App is ready, initializing...');
      
      // Setup auth manager IPC handlers
      this.authManager.setupIPC();
      
      // Create system tray first
      this.trayManager.createTray();
      
      // Create main window
      this.windowManager.createMainWindow();
      this.authManager.setMainWindow(this.windowManager.mainWindow);
      this.setupWindowEventHandlers();
      
      // Load initial page based on authentication status
      await this.loadInitialPage();
      
      // Start game detection asynchronously if enabled
      if (this.store.get('autoFindGames', true)) {
        console.log('🎮 Starting automatic game detection...');
        this.findGamesAsync();

        // Start lightweight game detection
        console.log('🚀 Starting lightweight game detection...');
        await this.lightweightGameDetector.startMonitoring();
        this.setupLightweightGameDetectionEvents();
      } else {
        console.log('🎮 Automatic game detection disabled in settings');
      }
      
      // Handle app activation (macOS)
      app.on('activate', () => {
        if (BrowserWindow.getAllWindows().length === 0) {
          this.windowManager.createMainWindow();
          this.setupWindowEventHandlers();
          this.loadInitialPage();
        } else {
          // Show existing window
          this.windowManager.show();
        }
      });
    });

    // Handle custom protocol URLs (for OAuth callbacks)
    app.on('open-url', (event, url) => {
      console.log('📥 Custom protocol URL received via open-url:', url);
      event.preventDefault();
      this.handleProtocolUrl(url);
    });
    
    // Additional protocol handling for development
    app.on('second-instance', (event, commandLine, workingDirectory) => {
      console.log('🔄 Second instance detected - checking for protocol URLs');
      const protocolUrl = commandLine.find(arg => arg.startsWith('warcraftarena://'));
      if (protocolUrl) {
        console.log('📥 Protocol URL detected in second instance:', protocolUrl);
        this.handleProtocolUrl(protocolUrl);
      }
    });

    // Handle protocol URLs from command line arguments (Windows)
    app.on('ready', () => {
      // Check if app was opened with protocol URL
      if (process.argv.length > 1) {
        const protocolUrl = process.argv.find(arg => arg.startsWith('warcraftarena://'));
        if (protocolUrl) {
          console.log('📥 Protocol URL detected in command line:', protocolUrl);
          setTimeout(() => this.handleProtocolUrl(protocolUrl), 1000); // Delay to ensure app is ready
        }
      }
    });

    // Handle app window closed (modified for tray functionality)
    app.on('window-all-closed', () => {
      // On macOS, keep app running in tray even when all windows are closed
      // On other platforms, only quit if not minimized to tray
      if (process.platform !== 'darwin' && !this.store.get('closeToTray', true)) {
        app.quit();
      }
    });

    // Handle before quit to clean up tray
    app.on('before-quit', () => {
      this.trayManager.setQuitting(true);
      this.trayManager.destroy();
      this.gameDetector.stopPeriodicStatusCheck();
      if (this.lightweightGameDetector) {
        this.lightweightGameDetector.stopMonitoring();
      }
      if (this.screenshotManager) {
        this.screenshotManager.stopMonitoring();
      }
      if (this.matchResultManager) {
        this.matchResultManager.stopMonitoring();
      }
    });
  }

  setupWindowEventHandlers() {
    if (!this.windowManager.mainWindow) return;

    this.windowManager.mainWindow.setTitle('WC Arena Core');

    // Handle window minimize
    this.windowManager.mainWindow.on('minimize', () => {
      this.trayManager.handleWindowMinimize();
    });

    // Handle window close
    this.windowManager.mainWindow.on('close', (event) => {
      this.trayManager.handleWindowClose(event);
    });
  }

  async findGamesAsync() {
    try {
      console.log('Starting automatic game detection...');
      await this.gameDetector.findAllGames();
      await this.trayManager.updateEnhancedTrayMenu();
      this.trayManager.updateTooltip();
      
      // Start periodic running status checks with tray menu update callback
      this.gameDetector.startPeriodicStatusCheck(async () => {
        await this.trayManager.updateEnhancedTrayMenu();
      });
      
      // Start screenshot monitoring
      await this.screenshotManager.startWatching();
      
      // Start comprehensive match result monitoring
      await this.matchResultManager.startMonitoring();
      
      // Set up match result event handlers
      this.matchResultManager.on('matchResult', (result) => {
        console.log('🎯 Match result processed:', result);
        
        // Send to renderer process for UI updates
        if (this.windowManager.mainWindow && this.windowManager.mainWindow.webContents) {
          this.windowManager.mainWindow.webContents.send('match-result', result);
        }
      });
      
      console.log('Game detection completed.');
    } catch (error) {
      console.error('Automatic game detection failed:', error);
    }
  }

  registerProtocol() {
    // In development, we need to register the protocol with the correct path
    if (app.isPackaged) {
      // Production: Use the packaged app
      app.setAsDefaultProtocolClient('warcraftarena');
      console.log('✅ Protocol registered for production (packaged app)');
    } else {
      // Development: Use electron with main.js
      const electronPath = process.execPath;
      const mainPath = path.join(__dirname, 'main.js');
      
      console.log('🔧 Registering protocol for development:');
      console.log('  Electron path:', electronPath);
      console.log('  Main path:', mainPath);
      
      try {
        app.setAsDefaultProtocolClient('warcraftarena', electronPath, [mainPath]);
        console.log('✅ Protocol registered for development');
      } catch (error) {
        console.error('❌ Failed to register protocol:', error);
      }
    }
    
    console.log('✅ Protocol registered for warcraftarena://');
    
    // Protocol URL handling is done in initializeApp() method to avoid conflicts
  }

  async loadInitialPage() {
    // Always check authentication first, regardless of mode
    const savedUser = this.store.get('user');
    if (savedUser && this.store.get('rememberLogin')) {
      // Try to validate stored user with server
      try {
        const isValid = await this.authManager.validateStoredUser(savedUser);
        if (isValid) {
          // User is authenticated, now check their preferred mode
          await this.loadAuthenticatedUserPage();
          return;
        } else {
          // Validation failed - ensure all user data is cleared
          console.log('❌ Stored user validation failed - clearing all user data');
          this.store.delete('user');
          this.store.delete('authToken');
          this.store.delete('rememberLogin');
        }
      } catch (error) {
        console.log('❌ Stored user validation failed:', error.message);
        // Ensure all user data is cleared on error
        this.store.delete('user');
        this.store.delete('authToken');
        this.store.delete('rememberLogin');
      }
    }
    
    // No valid authentication found, show login page
    await this.windowManager.loadLoginPage();
  }

  async loadAuthenticatedUserPage() {
    console.log('🎯 Loading Arena Core Dashboard...');

    if (this.windowManager.mainWindow) {
      // Load the new Arena Core Dashboard
      this.windowManager.mainWindow.loadFile('dashboard.html');
      console.log('✅ Arena Core Dashboard loaded');
    } else {
      console.error('❌ Main window not available');
    }
  }

  /**
   * Setup event handlers for lightweight game detection
   */
  setupLightweightGameDetectionEvents() {
    this.lightweightGameDetector.on('gameStarted', (gameSession) => {
      console.log(`🎮 Advanced detection: Game started - ${gameSession.name} v${gameSession.version}`);

      // Notify tray
      if (this.trayManager) {
        this.trayManager.updateGameStatus(`Playing ${gameSession.name}`, true);
      }

      // Send to renderer if window exists
      if (this.windowManager.mainWindow) {
        this.windowManager.mainWindow.webContents.send('game-started', gameSession);
      }
    });

    this.lightweightGameDetector.on('gameEnded', (gameSession) => {
      console.log(`🎮 Advanced detection: Game ended - ${gameSession.name}`);

      // Notify tray
      if (this.trayManager) {
        this.trayManager.updateGameStatus('Ready to play', false);
      }

      // Send to renderer if window exists
      if (this.windowManager.mainWindow) {
        this.windowManager.mainWindow.webContents.send('game-ended', gameSession);
      }
    });

    this.lightweightGameDetector.on('resultDetected', (gameSession, outcome, method) => {
      console.log(`🎯 Result detected: ${outcome} via ${method} for ${gameSession.name}`);

      // Send to renderer if window exists
      if (this.windowManager.mainWindow) {
        this.windowManager.mainWindow.webContents.send('result-detected', {
          gameSession,
          outcome,
          method
        });
      }
    });

    this.lightweightGameDetector.on('gameResult', (gameSession) => {
      console.log(`✅ Final game result: ${gameSession.finalResult.outcome} (${gameSession.finalResult.confidence})`);

      // Show notification
      const { Notification } = require('electron');
      if (Notification.isSupported()) {
        new Notification({
          title: 'Game Result Detected',
          body: `${gameSession.finalResult.outcome.toUpperCase()} in ${gameSession.name}!`,
          icon: path.join(__dirname, 'assets', 'icon.png')
        }).show();
      }

      // Send to renderer if window exists
      if (this.windowManager.mainWindow) {
        this.windowManager.mainWindow.webContents.send('game-result', gameSession);
      }
    });

    this.lightweightGameDetector.on('autoReported', (gameSession, reportData) => {
      console.log(`📤 Auto-reported: ${gameSession.name} result to Arena`);

      // Show success notification
      const { Notification } = require('electron');
      if (Notification.isSupported()) {
        new Notification({
          title: 'Match Auto-Reported',
          body: `${gameSession.finalResult.outcome.toUpperCase()} result sent to Arena!`,
          icon: path.join(__dirname, 'assets', 'icon.png')
        }).show();
      }

      // Send to renderer if window exists
      if (this.windowManager.mainWindow) {
        this.windowManager.mainWindow.webContents.send('auto-reported', {
          gameSession,
          reportData
        });
      }
    });

    this.lightweightGameDetector.on('autoReportFailed', (gameSession, error) => {
      console.error(`❌ Auto-report failed for ${gameSession.name}:`, error.message);

      // Show error notification
      const { Notification } = require('electron');
      if (Notification.isSupported()) {
        new Notification({
          title: 'Auto-Report Failed',
          body: `Please manually report your ${gameSession.name} result`,
          icon: path.join(__dirname, 'assets', 'icon.png')
        }).show();
      }

      // Send to renderer if window exists
      if (this.windowManager.mainWindow) {
        this.windowManager.mainWindow.webContents.send('auto-report-failed', {
          gameSession,
          error: error.message
        });
      }
    });
  }

  setupIPC() {
    // Dashboard IPC handlers
    ipcMain.handle('get-stored-user', () => {
      return this.store.get('user');
    });

    ipcMain.handle('get-game-stats', () => {
      // TODO: Get actual game stats from database
      return {
        warcraft1: { played: 0, won: 0, lost: 0 },
        warcraft2: { played: 0, won: 0, lost: 0 },
        warcraft3: { played: 0, won: 0, lost: 0 }
      };
    });

    ipcMain.handle('open-arena-website', () => {
      const { shell } = require('electron');
      shell.openExternal('http://localhost:3001');
    });

    ipcMain.handle('open-manual-report', (event, gameSession) => {
      const { shell } = require('electron');
      shell.openExternal('http://localhost:3001/views/ladder.html#report');
    });

    ipcMain.handle('get-settings', () => {
      return {
        autoFindGames: this.store.get('autoFindGames', true),
        autoReportMatches: this.store.get('autoReportMatches', true),
        minimizeToTray: this.store.get('minimizeToTray', true),
        closeToTray: this.store.get('closeToTray', true)
      };
    });

    ipcMain.handle('update-settings', (event, settings) => {
      for (const [key, value] of Object.entries(settings)) {
        this.store.set(key, value);
      }
      return true;
    });

    // Handle OAuth timeout notifications
    ipcMain.handle('oauth:timeout', async (event) => {
      console.log('⏰ OAuth timeout notification received');
      
      // Show notification to user
      if (this.windowManager.mainWindow) {
        this.windowManager.mainWindow.webContents.send('oauth-timeout', {
          message: 'Login timed out. Please try again.'
        });
      }
      
      return { success: true };
    });

    // Navigation handlers
    ipcMain.handle('navigation:load-page', async (event, page) => {
      console.log('🧭 Load page request received:', page);
      try {
        if (this.windowManager.mainWindow) {
          await this.windowManager.loadPage(page);
          return { success: true };
        }
        return { success: false, error: 'Main window not available' };
      } catch (error) {
        console.error('❌ Load page error:', error);
        return { success: false, error: error.message };
      }
    });

    // Configuration handlers
    ipcMain.handle('config:get', async (event) => {
      console.log('⚙️ Config request received');
      return {
        success: true,
        config: {
          serverUrl: this.store.get('serverUrl', 'http://127.0.0.1:3001'),
          rememberLogin: this.store.get('rememberLogin', true),
          minimizeToTray: this.store.get('minimizeToTray', true),
          closeToTray: this.store.get('closeToTray', true),
          autoFindGames: this.store.get('autoFindGames', true)
        }
      };
    });

    ipcMain.handle('config:update', async (event, config) => {
      console.log('⚙️ Config update request received:', config);
      
      try {
        // Update stored configuration
        Object.keys(config).forEach(key => {
          this.store.set(key, config[key]);
        });
        
        // Update auth manager server URL if changed
        if (config.serverUrl && config.serverUrl !== this.authManager.serverUrl) {
          this.authManager.updateServerUrl(config.serverUrl);
        }
        
        return { success: true };
      } catch (error) {
        console.error('❌ Config update error:', error);
        return { success: false, error: error.message };
      }
    });

    // Game detection handlers
    ipcMain.handle('games:find', async (event) => {
      console.log('🎮 Find games request received');
      
      try {
        await this.gameDetector.findGames();
        const games = await this.gameDetector.getDetectedGames();
        
        // Update tray menu with new games
        await this.trayManager.updateEnhancedTrayMenu();
        
        return { success: true, games };
      } catch (error) {
        console.error('❌ Find games error:', error);
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('games:launch', async (event, gameId) => {
      console.log('🚀 Launch game request received:', gameId);
      
      try {
        await this.gameDetector.launchGame(gameId);
        return { success: true };
      } catch (error) {
        console.error('❌ Launch game error:', error);
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('games:get-detected', async (event) => {
      console.log('🎮 Get detected games request received');
      
      try {
        const games = await this.gameDetector.getDetectedGames();
        return { success: true, games };
      } catch (error) {
        console.error('❌ Get detected games error:', error);
        return { success: false, error: error.message };
      }
    });

    // Screenshot handlers
    ipcMain.handle('screenshots:get-path', async (event) => {
      console.log('📸 Get screenshots path request received');
      
      try {
        const path = this.screenshotManager.getScreenshotsPath();
        return { success: true, path };
      } catch (error) {
        console.error('❌ Get screenshots path error:', error);
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('screenshots:open-folder', async (event) => {
      console.log('📸 Open screenshots folder request received');
      
      try {
        const path = this.screenshotManager.getScreenshotsPath();
        await shell.openPath(path);
        return { success: true };
      } catch (error) {
        console.error('❌ Open screenshots folder error:', error);
        return { success: false, error: error.message };
      }
    });

    // Window management handlers
    ipcMain.handle('window:minimize', async (event) => {
      console.log('🪟 Minimize window request received');
      
      if (this.windowManager.mainWindow) {
        this.windowManager.mainWindow.minimize();
        return { success: true };
      }
      
      return { success: false, error: 'Main window not available' };
    });

    ipcMain.handle('window:close', async (event) => {
      console.log('🪟 Close window request received');
      
      if (this.windowManager.mainWindow) {
        this.windowManager.mainWindow.close();
        return { success: true };
      }
      
      return { success: false, error: 'Main window not available' };
    });

    // App info handlers
    ipcMain.handle('app:get-info', async (event) => {
      console.log('ℹ️ Get app info request received');
      
      return {
        success: true,
        info: {
          version: app.getVersion(),
          name: app.getName(),
          isPackaged: app.isPackaged,
          platform: process.platform,
          arch: process.arch,
          uptime: Date.now() - (this.store.get('appStartTime') || Date.now())
        }
      };
    });

    // Test protocol URL handler (for debugging)
    ipcMain.handle('test-protocol-url', async (event, url) => {
      console.log('🧪 Test protocol URL received:', url);
      try {
        console.log('🧪 About to call handleProtocolUrl with:', url);
        await this.handleProtocolUrl(url);
        console.log('🧪 handleProtocolUrl completed successfully');
        return { success: true, message: 'Protocol URL handled successfully' };
      } catch (error) {
        console.error('❌ Test protocol URL error:', error);
        return { success: false, error: error.message };
      }
    });

    // Fallback authentication success handler
    ipcMain.handle('auth:fallback-success', async (event, data) => {
      console.log('🔄 Fallback authentication success received:', data);
      try {
        // Store the user data
        this.store.set('user', data.user);
        
        // Only store token if it exists (for session-based auth, token might be null)
        if (data.token) {
          this.store.set('authToken', data.token);
        }
        
        this.store.set('rememberLogin', true);
        
        console.log('✅ Fallback auth data stored successfully');
        
        // Load the authenticated user page
        console.log('🎯 Loading authenticated user page via fallback...');
        await this.loadAuthenticatedUserPage();
        console.log('✅ Authenticated user page loaded via fallback');
        
        // Show and focus the window
        if (this.windowManager.mainWindow) {
          this.windowManager.mainWindow.show();
          this.windowManager.mainWindow.focus();
          console.log('✅ Window shown and focused via fallback');
        }
        
        return { success: true, message: 'Fallback authentication successful' };
      } catch (error) {
        console.error('❌ Fallback authentication error:', error);
        return { success: false, error: error.message };
      }
    });

    // Go back to login handler
    ipcMain.handle('auth:go-back-to-login', async (event) => {
      console.log('🔄 Go back to login request received');
      try {
        // Clear stored authentication data
        this.store.delete('user');
        this.store.delete('authToken');
        this.store.set('rememberLogin', false);
        
        // Load the login page
        await this.windowManager.loadLoginPage();
        console.log('✅ Back to login page loaded');
        
        return { success: true };
      } catch (error) {
        console.error('❌ Go back to login error:', error);
        return { success: false, error: error.message };
      }
    });
    
    // Get stored user data handler
    ipcMain.handle('get-stored-user', async (event) => {
      console.log('👤 Getting stored user data...');
      try {
        const user = this.store.get('user');
        console.log('👤 Stored user data:', user);
        return user;
      } catch (error) {
        console.error('❌ Error getting stored user:', error);
        return null;
      }
    });

    // Additional game handlers
    ipcMain.handle('games:find-all', async (event) => {
      console.log('🎮 Find all games request received');
      try {
        await this.gameDetector.findGames();
        const games = await this.gameDetector.getDetectedGames();
        return { success: true, games };
      } catch (error) {
        console.error('❌ Find all games error:', error);
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('games:add-manual', async (event, game) => {
      console.log('🎮 Add manual game request received:', game);
      try {
        // This would need to be implemented in the game detector
        return { success: true };
      } catch (error) {
        console.error('❌ Add manual game error:', error);
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('games:remove-manual', async (event, gameId) => {
      console.log('🎮 Remove manual game request received:', gameId);
      try {
        // This would need to be implemented in the game detector
        return { success: true };
      } catch (error) {
        console.error('❌ Remove manual game error:', error);
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('games:get-by-type', async (event, type) => {
      console.log('🎮 Get games by type request received:', type);
      try {
        const games = await this.gameDetector.getDetectedGames();
        const filteredGames = games.filter(game => game.type === type);
        return { success: true, games: filteredGames };
      } catch (error) {
        console.error('❌ Get games by type error:', error);
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('games:get-manual', async (event) => {
      console.log('🎮 Get manual games request received');
      try {
        // This would need to be implemented in the game detector
        return { success: true, games: [] };
      } catch (error) {
        console.error('❌ Get manual games error:', error);
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('games:update-manual', async (event, gameId, game) => {
      console.log('🎮 Update manual game request received:', gameId, game);
      try {
        // This would need to be implemented in the game detector
        return { success: true };
      } catch (error) {
        console.error('❌ Update manual game error:', error);
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('games:open-maps-folder', async (event, gameType) => {
      console.log('🎮 Open maps folder request received:', gameType);
      try {
        // This would need to be implemented
        return { success: true };
      } catch (error) {
        console.error('❌ Open maps folder error:', error);
        return { success: false, error: error.message };
      }
    });

    // Additional screenshot handlers
    ipcMain.handle('screenshots:take', async (event) => {
      console.log('📸 Take screenshot request received');
      try {
        const result = await this.screenshotManager.takeScreenshot();
        return { success: true, result };
      } catch (error) {
        console.error('❌ Take screenshot error:', error);
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('screenshots:get-recent', async (event, limit) => {
      console.log('📸 Get recent screenshots request received:', limit);
      try {
        // This would need to be implemented in the screenshot manager
        return { success: true, screenshots: [] };
      } catch (error) {
        console.error('❌ Get recent screenshots error:', error);
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('screenshots:get-stats', async (event) => {
      console.log('📸 Get screenshot stats request received');
      try {
        // This would need to be implemented in the screenshot manager
        return { success: true, stats: {} };
      } catch (error) {
        console.error('❌ Get screenshot stats error:', error);
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('screenshots:get-analysis-tools', async (event) => {
      console.log('📸 Get analysis tools request received');
      try {
        // This would need to be implemented in the screenshot manager
        return { success: true, tools: [] };
      } catch (error) {
        console.error('❌ Get analysis tools error:', error);
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('screenshots:analyze-image', async (event, imagePath) => {
      console.log('📸 Analyze image request received:', imagePath);
      try {
        // This would need to be implemented in the screenshot manager
        return { success: true, analysis: {} };
      } catch (error) {
        console.error('❌ Analyze image error:', error);
        return { success: false, error: error.message };
      }
    });

    // Match handlers
    ipcMain.handle('matches:get-recent', async (event, limit) => {
      console.log('🏆 Get recent matches request received:', limit);
      try {
        // This would need to be implemented in the match result manager
        return { success: true, matches: [] };
      } catch (error) {
        console.error('❌ Get recent matches error:', error);
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('matches:get-stats', async (event) => {
      console.log('🏆 Get match stats request received');
      try {
        // This would need to be implemented in the match result manager
        return { success: true, stats: {} };
      } catch (error) {
        console.error('❌ Get match stats error:', error);
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('matches:process-manual', async (event, matchData) => {
      console.log('🏆 Process manual match request received:', matchData);
      try {
        // This would need to be implemented in the match result manager
        return { success: true };
      } catch (error) {
        console.error('❌ Process manual match error:', error);
        return { success: false, error: error.message };
      }
    });

    // Dialog handlers
    ipcMain.handle('dialog:open-file', async (event, options) => {
      console.log('📁 Open file dialog request received:', options);
      try {
        const result = await dialog.showOpenDialog(this.windowManager.mainWindow, options);
        return { success: true, result };
      } catch (error) {
        console.error('❌ Open file dialog error:', error);
        return { success: false, error: error.message };
      }
    });

    // Settings handlers
    ipcMain.handle('settings:get', async (event) => {
      console.log('⚙️ Get settings request received');
      try {
        const settings = {
          serverUrl: this.store.get('serverUrl', 'http://127.0.0.1:3001'),
          rememberLogin: this.store.get('rememberLogin', true),
          minimizeToTray: this.store.get('minimizeToTray', true),
          closeToTray: this.store.get('closeToTray', true),
          autoFindGames: this.store.get('autoFindGames', true)
        };
        return { success: true, settings };
      } catch (error) {
        console.error('❌ Get settings error:', error);
        return { success: false, error: error.message };
      }
    });

    ipcMain.handle('settings:set', async (event, settings) => {
      console.log('⚙️ Set settings request received:', settings);
      try {
        Object.keys(settings).forEach(key => {
          this.store.set(key, settings[key]);
        });
        return { success: true };
      } catch (error) {
        console.error('❌ Set settings error:', error);
        return { success: false, error: error.message };
      }
    });
  }

  handleSingleInstance() {
    // Ensure only one instance of the app can run
    const gotTheLock = app.requestSingleInstanceLock();

    if (!gotTheLock) {
      console.log('Another instance is already running. Exiting...');
      app.quit();
    } else {
      // Handle when someone tries to run a second instance
      app.on('second-instance', (event, commandLine, workingDirectory) => {
        console.log('🔄 Second instance detected');
        console.log('Command line arguments:', commandLine);
        
        // Check if this is a protocol URL call
        const protocolUrl = commandLine.find(arg => arg.startsWith('warcraftarena://'));
        if (protocolUrl) {
          console.log('🔗 Protocol URL detected in second instance:', protocolUrl);
          this.handleProtocolUrl(protocolUrl);
        }
        
        // If we have a window, restore it and focus it
        if (this.windowManager.mainWindow) {
          if (this.windowManager.mainWindow.isMinimized()) {
            this.windowManager.mainWindow.restore();
          }
          this.windowManager.mainWindow.focus();
        }
      });
      
      console.log('✅ Single instance lock acquired');
    }
  }

  setupSingleInstanceHandling() {
    this.handleSingleInstance();
  }

  // Unified protocol URL handler
  async handleProtocolUrl(url) {
    console.log('🔄 Processing protocol URL:', url);
    
    try {
      if (url.startsWith('warcraftarena://oauth-success')) {
        console.log('🎉 OAuth success callback detected!');
        
        // Ensure auth manager is ready
        if (!this.authManager) {
          console.error('❌ Auth manager not initialized');
          return;
        }
        
        // Handle the OAuth callback
        const result = await this.authManager.handleOAuthCallback(url);
        
        if (result.success) {
          console.log('✅ OAuth callback processed successfully');
          
          // Only load the authenticated user page if this is a new token
          if (!result.alreadyProcessed) {
            console.log('🎯 About to load authenticated user page...');
            await this.loadAuthenticatedUserPage();
            console.log('✅ Authenticated user page loaded successfully');
            
            // Show and focus the window
            if (this.windowManager.mainWindow) {
              this.windowManager.mainWindow.show();
              this.windowManager.mainWindow.focus();
              console.log('✅ Window shown and focused');
            }
            
            // Notify renderer process of successful login
            if (this.windowManager.mainWindow) {
              console.log('📡 Sending oauth-success event to renderer...');
              this.windowManager.mainWindow.webContents.send('oauth-success', {
                message: 'Login successful!'
              });
              console.log('✅ oauth-success event sent to renderer');
            }
          } else {
            console.log('🔄 Token already processed, just focusing window');
            if (this.windowManager.mainWindow) {
              this.windowManager.mainWindow.show();
              this.windowManager.mainWindow.focus();
            }
          }
        } else {
          console.error('❌ OAuth callback failed:', result.error);
          
          // Show error and redirect to login
          if (this.windowManager.mainWindow) {
            this.windowManager.mainWindow.webContents.send('oauth-error', {
              error: result.error
            });
            this.windowManager.loadLoginPage();
          }
        }
      } else {
        console.log('⚠️ Unknown protocol URL format:', url);
      }
    } catch (error) {
      console.error('❌ Error processing protocol URL:', error);
      
      // Show error to user
      if (this.windowManager.mainWindow) {
        this.windowManager.mainWindow.webContents.send('oauth-error', {
          error: 'Failed to process login callback'
        });
      }
    }
  }
}

// Create the app instance
const warcraftArenaApp = new WarcraftArenaApp();

// Handle certificate errors (for development)
app.on('certificate-error', (event, webContents, url, error, certificate, callback) => {
  if (url.startsWith('http://localhost:')) {
    event.preventDefault();
    callback(true);
  } else {
    callback(false);
  }
}); 