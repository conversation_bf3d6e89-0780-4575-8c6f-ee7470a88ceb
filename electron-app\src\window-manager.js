const { BrowserWindow, shell, app } = require('electron');
const path = require('path');

class WindowManager {
  constructor(store) {
    this.store = store;
    this.mainWindow = null;
  }

  createMainWindow() {
    // Get saved window bounds or use defaults
    const bounds = this.store.get('windowBounds');
    
    this.mainWindow = new BrowserWindow({
      width: bounds.width,
      height: bounds.height,
      minWidth: 1000,
      minHeight: 700,
      icon: this.getIconPath(),
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        enableRemoteModule: false,
        preload: path.join(__dirname, '..', 'dashboard-preload.js'),
        webSecurity: true,
        allowRunningInsecureContent: false,
        // Set CSP to allow connections to our server and external resources
        additionalArguments: [
          '--disable-web-security',
          '--allow-file-access-from-files',
          '--disable-features=VizDisplayCompositor'
        ]
      },
      show: false,
      titleBarStyle: 'default',
      autoHideMenuBar: true,
      title: 'WC Arena Core',
      // Performance optimizations
      backgroundColor: '#1a1a2e',
      transparent: false,
      hasShadow: false,
      // Disable animations for better performance
      animated: false
    });

    // Set CSP headers to allow connections to our server and external resources
    // TEMPORARILY DISABLED for testing
    /*
    this.mainWindow.webContents.session.webRequest.onHeadersReceived((details, callback) => {
      callback({
        responseHeaders: {
          ...details.responseHeaders,
          'Content-Security-Policy': [
            "default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; " +
            "script-src 'self' 'unsafe-inline' 'unsafe-eval' http://127.0.0.1:3001 http://localhost:3001 https://warcraftarena.com https://cdnjs.cloudflare.com https://cdn.jsdelivr.net https://*.cloudflare.com https://*.jsdelivr.net; " +
            "style-src 'self' 'unsafe-inline' http://127.0.0.1:3001 http://localhost:3001 https://warcraftarena.com https://cdnjs.cloudflare.com https://cdn.jsdelivr.net https://fonts.googleapis.com https://*.cloudflare.com https://*.jsdelivr.net; " +
            "img-src 'self' data: blob: http://127.0.0.1:3001 http://localhost:3001 https://warcraftarena.com https://cdnjs.cloudflare.com https://cdn.jsdelivr.net https://ssl.gstatic.com https://fonts.gstatic.com https://*.cloudflare.com https://*.jsdelivr.net; " +
            "font-src 'self' data: https://fonts.gstatic.com https://cdnjs.cloudflare.com https://cdn.jsdelivr.net https://*.cloudflare.com https://*.jsdelivr.net; " +
            "connect-src 'self' http://127.0.0.1:3001 http://localhost:3001 https://warcraftarena.com wss://127.0.0.1:3001 ws://127.0.0.1:3001 https://*.cloudflare.com https://*.jsdelivr.net; " +
            "frame-src 'self' https://accounts.google.com https://discord.com https://id.twitch.tv; " +
            "object-src 'none'; " +
            "base-uri 'self'; " +
            "form-action 'self' http://127.0.0.1:3001 http://localhost:3001 https://warcraftarena.com; " +
            "upgrade-insecure-requests"
          ]
        }
      });
    });
    */

    // Remove menu completely for cleaner interface
    this.mainWindow.setMenu(null);

    // Save window bounds when they change
    this.mainWindow.on('resize', () => {
      const bounds = this.mainWindow.getBounds();
      this.store.set('windowBounds', bounds);
    });

    this.mainWindow.on('moved', () => {
      const bounds = this.mainWindow.getBounds();
      this.store.set('windowBounds', bounds);
    });

    this.mainWindow.once('ready-to-show', () => {
      this.mainWindow.show();
      
      // Focus the window
      this.mainWindow.focus();
    });

    // Handle external links
    this.mainWindow.webContents.setWindowOpenHandler(({ url }) => {
      console.log('Window open handler called for URL:', url);
      // Allow navigation to our server URL within iframes, but open other links externally
      const serverUrl = this.store.get('serverUrl');
      if (url.startsWith(serverUrl)) {
        console.log('Allowing server URL to load within app:', url);
        return { action: 'allow' }; // Allow server URLs to load in iframes
      } else {
        console.log('Opening external URL in system browser:', url);
        shell.openExternal(url);
        return { action: 'deny' };
      }
    });

    // Prevent navigation to external sites (but allow iframe navigation)
    this.mainWindow.webContents.on('will-navigate', (event, navigationUrl) => {
      console.log('Will navigate event:', navigationUrl);
      const allowedOrigins = [
        this.store.get('serverUrl'),
        'file://',
        'warcraftarena://',
        'https://warcraftarena.com',  // Always allow production server
        'http://localhost:3001'      // Always allow local development
      ];
      
      const isAllowed = allowedOrigins.some(origin => navigationUrl.startsWith(origin));
      console.log('Navigation allowed:', isAllowed, 'for URL:', navigationUrl);
      
      // Only prevent navigation if it's the main frame navigating to an external site
      // Allow iframe navigation to our server
      if (!isAllowed && event.frame === this.mainWindow.webContents.mainFrame) {
        console.log('Preventing main frame navigation to external site:', navigationUrl);
        event.preventDefault();
        shell.openExternal(navigationUrl);
      }
    });

    // Clean up when window is closed
    this.mainWindow.on('closed', () => {
      this.mainWindow = null;
    });

    // Add keyboard shortcut for developer tools (for debugging)
    this.mainWindow.webContents.on('before-input-event', (event, input) => {
      // Ctrl+Shift+I (or Cmd+Shift+I on Mac) to open developer tools
      if (input.control && input.shift && input.key.toLowerCase() === 'i') {
        console.log('🔧 Opening developer tools via keyboard shortcut...');
        this.mainWindow.webContents.openDevTools();
      }
      // F12 to open developer tools
      if (input.key === 'F12') {
        console.log('🔧 Opening developer tools via F12...');
        this.mainWindow.webContents.openDevTools();
      }
    });

    // Handle window focus for better UX
    this.mainWindow.on('focus', () => {
      this.mainWindow.webContents.executeJavaScript(`
        if (window.electronAPI && window.electronAPI.onWindowFocus) {
          window.electronAPI.onWindowFocus();
        }
      `).catch(err => {
        // Ignore errors if the method doesn't exist
      });
    });

    return this.mainWindow;
  }

  async loadLoginPage() {
    if (!this.mainWindow) return;
    
    // Load the login page from server URL (not local file) for proper OAuth and CSS loading
    const serverUrl = this.store.get('serverUrl') || 'http://127.0.0.1:3001';
    const loginUrl = `${serverUrl}/views/login.html?electron=true&electronApp=WC_Arena_Core`;
    
    console.log('🔐 Loading login page from server:', loginUrl);
    
    // Add retry mechanism for connection issues
    let retries = 0;
    const maxRetries = 5;
    const retryDelay = 2000; // 2 seconds
    
    const attemptLoad = async () => {
      try {
        await this.mainWindow.loadURL(loginUrl);
        console.log('✅ Login page loaded successfully');
      } catch (error) {
        console.error(`❌ Failed to load login page (attempt ${retries + 1}/${maxRetries}):`, error.message);
        
        if (retries < maxRetries) {
          retries++;
          console.log(`🔄 Retrying in ${retryDelay/1000} seconds...`);
          setTimeout(attemptLoad, retryDelay);
        } else {
          console.error('❌ Max retries reached, showing error page');
          this.loadErrorPage('Failed to connect to server. Please ensure the backend and proxy are running.');
        }
      }
    };
    
    await attemptLoad();
  }

  loadMainApp() {
    if (!this.mainWindow) return;
    
    const serverUrl = this.store.get('serverUrl');
    this.mainWindow.loadURL(serverUrl);
  }

  loadFullscreenWebsite() {
    if (!this.mainWindow) {
      console.error('❌ Cannot load fullscreen website: main window not available');
      return;
    }
    
    console.log('🌐 Loading fullscreen website...');
    // Load the custom fullscreen website page that embeds the website in a clean iframe
    const fullscreenWebsitePath = path.join(__dirname, '..', 'pages', 'fullscreen-website.html');
    console.log('📁 Fullscreen website path:', fullscreenWebsitePath);
    this.mainWindow.loadFile(fullscreenWebsitePath);
    console.log('✅ Fullscreen website load initiated');
  }

  async loadPage(page) {
    if (!this.mainWindow) return;
    
    console.log('🧭 Loading page:', page);
    
    try {
      switch (page) {
        case 'login':
          await this.loadLoginPage();
          break;
        case 'main':
        case 'app':
          this.loadMainApp();
          break;
        case 'fullscreen':
        case 'website':
          this.loadFullscreenWebsite();
          break;
        default:
          console.log('⚠️ Unknown page requested:', page);
          // Default to main app
          this.loadMainApp();
      }
    } catch (error) {
      console.error('❌ Error loading page:', page, error);
      this.loadErrorPage({ message: `Failed to load page: ${page}`, error: error.message });
    }
  }

  loadErrorPage(error) {
    if (!this.mainWindow) return;
    
    const errorHtml = `
      <!DOCTYPE html>
      <html>
        <head>
          <title>Error - WC Arena Core</title>
          <style>
            body {
              font-family: Arial, sans-serif;
              background: #1a1a2e;
              color: white;
              display: flex;
              align-items: center;
              justify-content: center;
              height: 100vh;
              margin: 0;
              padding: 20px;
            }
            .error-container {
              text-align: center;
              max-width: 500px;
            }
            .error-icon {
              font-size: 48px;
              margin-bottom: 20px;
            }
            .error-title {
              font-size: 24px;
              margin-bottom: 15px;
              color: #ff6b6b;
            }
            .error-message {
              font-size: 16px;
              line-height: 1.5;
              margin-bottom: 20px;
            }
            .retry-button {
              background: #4ecdc4;
              color: white;
              border: none;
              padding: 12px 24px;
              border-radius: 6px;
              cursor: pointer;
              font-size: 16px;
            }
            .retry-button:hover {
              background: #45b7aa;
            }
          </style>
        </head>
        <body>
          <div class="error-container">
            <div class="error-icon">⚠️</div>
            <div class="error-title">Connection Error</div>
            <div class="error-message">${typeof error === 'string' ? error : error.message || 'Unknown error occurred'}</div>
            <button class="retry-button" onclick="window.location.reload()">Retry</button>
          </div>
        </body>
      </html>
    `;
    
    this.mainWindow.loadURL(`data:text/html,${encodeURIComponent(errorHtml)}`);
  }

  getIconPath() {
    const iconDir = path.join(__dirname, '..', 'assets');
    
    switch (process.platform) {
      case 'win32':
        return path.join(iconDir, 'icon.ico');
      case 'darwin':
        return path.join(iconDir, 'icon.icns');
      default:
        return path.join(iconDir, 'icon.png');
    }
  }

  // Utility methods
  show() {
    if (this.mainWindow) {
      if (this.mainWindow.isMinimized()) {
        this.mainWindow.restore();
      }
      this.mainWindow.show();
      this.mainWindow.focus();
    }
  }

  hide() {
    if (this.mainWindow) {
      this.mainWindow.hide();
    }
  }

  close() {
    if (this.mainWindow) {
      this.mainWindow.close();
    }
  }

  isMinimized() {
    return this.mainWindow ? this.mainWindow.isMinimized() : false;
  }

  isVisible() {
    return this.mainWindow ? this.mainWindow.isVisible() : false;
  }

  setTitle(title) {
    if (this.mainWindow) {
      this.mainWindow.setTitle(title);
    }
  }

  // Send data to the renderer process
  sendToRenderer(channel, data) {
    if (this.mainWindow && this.mainWindow.webContents) {
      this.mainWindow.webContents.send(channel, data);
    }
  }

  // Execute JavaScript in the renderer process
  executeJavaScript(code) {
    if (this.mainWindow && this.mainWindow.webContents) {
      return this.mainWindow.webContents.executeJavaScript(code);
    }
    return Promise.resolve();
  }
}

module.exports = { WindowManager }; 